#!/usr/bin/env python3
"""CLI utility to generate service account tokens."""
import argparse
import sys
from pathlib import Path

from qw_config.loader import load_config
from qw_log.factory import QwLogFactory
from qw_mono.config import QwMonoConfig
from qw_pfoertner.service.service_account_auth import ServiceAccountAuthenticationService


def main() -> None:
    """Generate a service account token."""
    parser = argparse.ArgumentParser("generate-service-token")
    parser.add_argument("--qw-mono-config", metavar="FILE", type=Path, required=True)
    parser.add_argument("--qw-mono-overwrite-config", metavar="FILE", type=Path, default=None)
    parser.add_argument("--service-name", type=str, required=True, help="Name of the service")
    parser.add_argument("--output-format", choices=["token", "env"], default="token", 
                       help="Output format: 'token' for just the token, 'env' for environment variable format")
    
    args = parser.parse_args()
    
    try:
        # Load configuration
        config = load_config(QwMonoConfig, args.qw_mono_config, args.qw_mono_overwrite_config)
        
        # Initialize logging
        log_factory = QwLogFactory("0.0.0")
        
        # Create service account authentication service
        service_auth_service = ServiceAccountAuthenticationService.from_config(
            config.mono_pfoertner.service_auth_settings, 
            log_factory
        )
        
        # Check if service is allowed
        if not service_auth_service.is_service_allowed(args.service_name):
            print(f"Error: Service '{args.service_name}' is not in the allowed services list", file=sys.stderr)
            print(f"Allowed services: {list(service_auth_service._allowed_services.keys())}", file=sys.stderr)
            sys.exit(1)
        
        # Generate token
        token = service_auth_service.issue_service_token(args.service_name)
        if not token:
            print(f"Error: Failed to generate token for service '{args.service_name}'", file=sys.stderr)
            sys.exit(1)
        
        # Output token
        if args.output_format == "env":
            print(f"SERVICE_ACCOUNT_TOKEN={token}")
        else:
            print(token)
            
    except Exception as e:
        print(f"Error: {e}", file=sys.stderr)
        sys.exit(1)


if __name__ == "__main__":
    main()
