"""Service account authentication service."""
from datetime import datetime, timedelta
from functools import partial
from typing import Any, Dict, List

from joserfc import jwk, jwt
from joserfc.errors import JoseError
from pydantic import BaseModel, Field, ValidationError

from qw_basic_iam_policy.service_account import ServiceAccountAuthContext, ServiceAccountToken
from qw_log_interface import NO_LOG_FACTORY, LogFactory


class ServiceAccountSettings(BaseModel):
    """Configuration settings for service account authentication."""

    service_account_key: str
    token_expiration_hours: int = 24
    allowed_services: List[Dict[str, Any]] = Field(default_factory=list)

    @property
    def token_expiration(self) -> timedelta:
        """Get token expiration as timedelta."""
        return timedelta(hours=self.token_expiration_hours)


class ServiceAccountAuthenticationService:
    """Service for issuing and validating service account tokens."""

    def __init__(self, settings: ServiceAccountSettings, lf: LogFactory = NO_LOG_FACTORY):
        self.settings = settings
        self.key_data = jwk.OctKey.import_key(settings.service_account_key)
        self.logger = lf.get_logger(__name__)

        # Build allowed services lookup
        self._allowed_services = {}
        for service_config in settings.allowed_services:
            service_name = service_config.get("name")
            service_scopes = service_config.get("scopes", [])
            if service_name:
                self._allowed_services[service_name] = service_scopes

    def issue_service_token(self, service_name: str) -> str | None:
        """Issue JWT token for service-to-service communication."""
        if service_name not in self._allowed_services:
            self.logger.error(f"Service '{service_name}' is not in allowed services list")
            return None

        scopes = self._allowed_services[service_name]
        expiration = datetime.utcnow() + self.settings.token_expiration

        payload = ServiceAccountToken(
            service_name=service_name,
            subject=f"service:{service_name}",
            scopes=scopes,
            expiration=expiration
        )

        try:
            token = jwt.encode({"alg": "HS256"}, payload.model_dump(mode="json"), self.key_data)
            self.logger.info(f"Issued service token for '{service_name}' with scopes: {scopes}")
            return token
        except JoseError as e:
            self.logger.error(f"Failed to encode service token for '{service_name}': {e}")
            return None

    def validate_service_token(self, token: str) -> ServiceAccountAuthContext | None:
        """Validate service account token and return auth context."""
        try:
            decoded = jwt.decode(token, self.key_data)
            service_token = ServiceAccountToken(**decoded.claims)
        except (JoseError, ValidationError) as e:
            self.logger.warning(f"Failed to decode service token: {e}")
            return None

        # Check expiration
        if service_token.is_expired():
            self.logger.warning(f"Service token for '{service_token.service_name}' has expired")
            return None

        # Verify service is still allowed
        if service_token.service_name not in self._allowed_services:
            self.logger.warning(f"Service '{service_token.service_name}' is no longer allowed")
            return None

        # Verify scopes match current configuration
        current_scopes = self._allowed_services[service_token.service_name]
        if set(service_token.scopes) != set(current_scopes):
            self.logger.warning(
                f"Service '{service_token.service_name}' token scopes don't match current config. "
                f"Token scopes: {service_token.scopes}, Current scopes: {current_scopes}"
            )
            return None

        self.logger.info(f"Validated service token for '{service_token.service_name}'")
        return ServiceAccountAuthContext(
            service_name=service_token.service_name,
            scopes=service_token.scopes,
            subject=service_token.subject
        )

    def get_service_scopes(self, service_name: str) -> List[str]:
        """Get allowed scopes for a service."""
        return self._allowed_services.get(service_name, [])

    def is_service_allowed(self, service_name: str) -> bool:
        """Check if a service is in the allowed list."""
        return service_name in self._allowed_services

    @classmethod
    def from_config(cls, settings: ServiceAccountSettings, lf: LogFactory = NO_LOG_FACTORY) -> "ServiceAccountAuthenticationService":
        """Create service from configuration."""
        return cls(settings=settings, lf=lf)
