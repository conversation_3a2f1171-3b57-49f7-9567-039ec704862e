"""Thread-local request context for storing authentication information."""
import threading
from typing import Optional

from qw_basic_iam_policy.service_account import ServiceAccountAuthContext


class RequestContext:
    """Thread-local storage for request-scoped data."""
    
    def __init__(self):
        self.service_account_auth: Optional[ServiceAccountAuthContext] = None
    
    def clear(self):
        """Clear all context data."""
        self.service_account_auth = None


# Thread-local storage
_context = threading.local()


def get_request_context() -> RequestContext:
    """Get the current request context."""
    if not hasattr(_context, 'request_context'):
        _context.request_context = RequestContext()
    return _context.request_context


def set_service_account_auth(auth_context: ServiceAccountAuthContext) -> None:
    """Set service account authentication context for the current request."""
    get_request_context().service_account_auth = auth_context


def get_service_account_auth() -> Optional[ServiceAccountAuthContext]:
    """Get service account authentication context for the current request."""
    return get_request_context().service_account_auth


def clear_request_context() -> None:
    """Clear the request context."""
    get_request_context().clear()
