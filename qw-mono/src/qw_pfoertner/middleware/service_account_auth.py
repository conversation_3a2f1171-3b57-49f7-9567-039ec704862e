"""Falcon middleware for service account authentication."""
import falcon

from qw_basic_iam_policy.service_account import ServiceAccountAuthContext
from qw_log_interface import NO_LOG_FACTORY, LogFactory
from qw_pfoertner.context.request_context import set_service_account_auth, clear_request_context
from qw_pfoertner.service.service_account_auth import ServiceAccountAuthenticationService


class ServiceAccountAuthMiddleware:
    """
    Falcon middleware that handles service account authentication.

    This middleware checks for Authorization Bearer tokens and validates them
    as service account tokens. If valid, it stores the service account context
    in thread-local storage for use by downstream handlers.
    """

    def __init__(self, service_auth_service: ServiceAccountAuthenticationService, lf: LogFactory = NO_LOG_FACTORY):
        self.service_auth_service = service_auth_service
        self.logger = lf.get_logger(__name__)

    def process_request(self, req: falcon.Request, resp: falcon.Response) -> None:
        """Process incoming request to check for service account authentication."""
        # Clear any previous context
        clear_request_context()

        # Check for Authorization header
        auth_header = req.get_header("Authorization")
        if not auth_header or not auth_header.startswith("Bearer "):
            # No Bearer token, continue with normal processing
            return

        # Extract token
        token = auth_header[7:]  # Remove "Bearer " prefix

        # Validate service account token
        service_auth_context = self.service_auth_service.validate_service_token(token)
        if service_auth_context:
            # Store service account context in thread-local storage
            set_service_account_auth(service_auth_context)
            self.logger.info(f"Service account authenticated: {service_auth_context.service_name}")
        else:
            # Invalid service account token
            self.logger.warning(f"Invalid service account token provided")
            raise falcon.HTTPUnauthorized(
                title="Unauthorized",
                description="Invalid service account token"
            )

    def process_response(self, req: falcon.Request, resp: falcon.Response, resource, req_succeeded: bool) -> None:
        """Clean up thread-local context after request processing."""
        clear_request_context()
