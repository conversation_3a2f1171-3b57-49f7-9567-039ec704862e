"""Document Intelligence Agent that uses MCP tools instead of service injection."""
from typing import Any, Dict, Optional, cast
from uuid import UUID

from pydantic_ai import Agent as PydanticAgent, RunContext
from pydantic_ai.models.openai import OpenAIModel
from pydantic_ai.providers.openai import OpenAIProvider
from pydantic_ai.settings import ModelSettings
from pydantic_ai.usage import Usage
from fastmcp import Client

from qw_log_interface import NO_LOG_FACTORY, LogFactory
from qw_trunk.service.agent.models import AgentContext, AgentResponse, AgentActionResponse, AgentTextResponse, AgentErrorResponse


class DocumentIntelligenceMCPAgent:
    """Document Intelligence Agent that uses MCP tools for file operations and analysis."""
    
    def __init__(
        self,
        api_key: str,
        mcp_client: Optional[Client] = None,
        model_name: str = "gpt-4o",
        temperature: float = 0.2,
        timeout: float = 30.0,
        lf: LogFactory = NO_LOG_FACTORY,
    ):
        """Initialize the document intelligence agent with MCP client."""
        self.name = "document_intelligence_agent"
        self.description = "Agent specialized for cross document auditing tasks using MCP tools"
        self.api_key = api_key
        self.mcp_client = mcp_client
        self.model_name = model_name
        self.temperature = temperature
        self.timeout = timeout
        self.logger = lf.get_logger(__name__)
        self.lf = lf
        self.agent_available = False
        self.usage = Usage()
        
        self._configure_agent()
    
    def _configure_agent(self) -> None:
        """Configure the pydantic-ai agent with MCP tools."""
        try:
            # Prepare model settings
            model_settings = ModelSettings(
                temperature=self.temperature,
                timeout=self.timeout,
            )
            
            # Setup OpenAI model with provider
            provider = OpenAIProvider(api_key=self.api_key)
            
            # Extract model name without provider prefix if needed
            model_name = self.model_name
            if model_name.startswith("openai:"):
                model_name = model_name.split(":")[-1]
            
            model = OpenAIModel(model_name, provider=provider)
            
            # Create the agent
            self.agent = PydanticAgent[AgentContext, str](
                model=model,
                model_settings=model_settings,
                retries=2,
                system_prompt=self._get_system_prompt(),
                deps_type=AgentContext,
            )
            
            # Register MCP tools
            self._register_mcp_tools()
            
            self.agent_available = True
            self.logger.info("Document intelligence MCP agent configured successfully")
            
        except Exception as e:
            self.logger.error(f"Failed to configure document intelligence MCP agent: {e}")
            self.agent_available = False
    
    def _get_system_prompt(self) -> str:
        """Get the system prompt for the document intelligence agent."""
        return """
        You are a specialized assistant for answering questions about files associated with a single order line.
        You use MCP tools to access file information and perform analysis.

        You can help users:
        1. Check all files associated with the current order line
        2. Retrieve analysis for files labeled as technical drawing or material certificate
        3. Compare information across different files to identify inconsistencies or relationships

        Available MCP tools:
        - get_context: Get the current context of frontend
        - list_available_mcp_tools: List all available MCP tools
        - call_mcp_tool: Call any MCP tool by name with arguments

        When working with files:
        1. First get the context to understand what files are available
        2. Use appropriate MCP tools to retrieve file information or analysis
        3. For technical drawings, look for MCP tools related to drawing analysis
        4. For material certificates, look for MCP tools related to material certificate analysis
        5. Always provide clear, helpful responses about the file analysis results

        Focus on cross-document analysis and identifying relationships or inconsistencies between files.
        Always be helpful and provide actionable insights based on the file analysis.
        """
    
    def _register_mcp_tools(self) -> None:
        """Register MCP tools with the agent."""
        
        @self.agent.tool
        async def get_context(ctx: RunContext[AgentContext]) -> Dict[str, Any]:
            """Get the current context of the agent."""
            context_data = cast(Dict[str, Any], ctx.deps)
            return {
                "session_id": context_data.get("session_id"),
                "user_context": context_data.get("user_context", {}),
                "available_actions": context_data.get("available_actions", [])
            }
        
        @self.agent.tool
        async def list_available_mcp_tools(ctx: RunContext[AgentContext]) -> str:
            """List all available MCP tools."""
            if not self.mcp_client:
                return "MCP client not available"
            
            try:
                async with self.mcp_client:
                    tools = await self.mcp_client.list_tools()
                    if tools:
                        tool_list = []
                        for tool in tools:
                            description = getattr(tool, 'description', 'No description available')
                            tool_list.append(f"- {tool.name}: {description}")
                        return f"Available MCP tools ({len(tools)}):\n" + "\n".join(tool_list)
                    else:
                        return "No MCP tools are currently available"
            except Exception as e:
                self.logger.error(f"Failed to list MCP tools: {e}")
                return f"Error listing MCP tools: {e}"
        
        @self.agent.tool
        async def call_mcp_tool(ctx: RunContext[AgentContext], tool_name: str, arguments: Optional[Dict[str, Any]] = None) -> str:
            """Call an MCP tool by name with arguments."""
            if not self.mcp_client:
                return "MCP client not available"
            
            try:
                async with self.mcp_client:
                    result = await self.mcp_client.call_tool(tool_name, arguments or {})
                    # Extract content from result
                    if result and len(result) > 0:
                        content = result[0]
                        # Try to get text content, fallback to string representation
                        try:
                            if hasattr(content, 'text'):
                                return str(getattr(content, 'text', ''))
                            else:
                                return str(content)
                        except Exception:
                            return str(content)
                    return f"Tool {tool_name} executed successfully"
            except Exception as e:
                self.logger.error(f"MCP tool execution failed: {e}")
                return f"Error executing MCP tool {tool_name}: {e}"
    
    async def process(
        self,
        prompt: str,
        context: Dict[str, Any],
        usage: Optional[Usage] = None,
    ) -> AgentResponse:
        """Process a user prompt and return a response."""
        if not self.agent_available:
            self.logger.error("Document intelligence agent not available for processing")
            return AgentErrorResponse(error="Agent not available")
        
        try:
            # Prepare context as AgentContext
            agent_context: AgentContext = {
                "session_id": context.get("session_id", ""),
                "user_context": context.get("user_context", {}),
                "available_actions": context.get("available_actions", [])
            }
            
            # Run the agent
            result = await self.agent.run(prompt, deps=agent_context, usage=usage)
            
            # Return response as AgentTextResponse
            response_message = result.output if result.output else "I'm here to help with document analysis!"
            
            self.logger.info(f"Document intelligence agent processed prompt successfully: {len(response_message)} chars")
            
            return AgentTextResponse(message=response_message)
            
        except Exception as e:
            self.logger.error(f"Error processing prompt with document intelligence agent: {e}")
            return AgentErrorResponse(error=f"Error processing request: {e}")
    
    async def health_check(self) -> bool:
        """Check if the agent is healthy and ready to process requests."""
        return self.agent_available
