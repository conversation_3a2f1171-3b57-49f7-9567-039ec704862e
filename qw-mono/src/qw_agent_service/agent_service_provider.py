"""Service provider for qw_agent_service that provides access to business services."""
from typing import Any, Dict, Optional

from qw_log_interface import NO_LOG_FACTORY, LogFactory
from qw_trunk.service.agent.service_provider import ServiceProvider
from qw_trunk.service.drawing.technical_drawing_analysis_client import TechnicalDrawingAnalysisClient
from qw_trunk.service.material_certificate.material_certificate import MaterialCertificateService
from qw_trunk.service.resource.file import FileResourceService
from qw_trunk.service.resource.s3_object import S3ObjectService


class AgentServiceProvider(ServiceProvider):
    """Service provider for agent service that provides access to business services."""
    
    def __init__(
        self,
        runtime_service_url: str,
        lf: LogFactory = NO_LOG_FACTORY,
    ):
        """Initialize the agent service provider."""
        self.runtime_service_url = runtime_service_url
        self.logger = lf.get_logger(__name__)
        self.lf = lf
        
        # Initialize service instances
        self._file_service: Optional[FileResourceService] = None
        self._s3_service: Optional[S3ObjectService] = None
        self._drawing_analysis_client: Optional[TechnicalDrawingAnalysisClient] = None
        self._material_certificate_service: Optional[MaterialCertificateService] = None
    
    def get_file_service(self) -> FileResourceService:
        """Get file resource service."""
        if self._file_service is None:
            # Create HTTP-based file service client
            # For now, we'll create a placeholder that would connect to the runtime service
            # In a real implementation, this would be an HTTP client
            self.logger.warning("File service not implemented for agent service provider")
            raise NotImplementedError("File service not available in agent service provider")
        return self._file_service
    
    def get_s3_service(self) -> S3ObjectService:
        """Get S3 object service."""
        if self._s3_service is None:
            # Create HTTP-based S3 service client
            # For now, we'll create a placeholder that would connect to the runtime service
            # In a real implementation, this would be an HTTP client
            self.logger.warning("S3 service not implemented for agent service provider")
            raise NotImplementedError("S3 service not available in agent service provider")
        return self._s3_service
    
    def get_drawing_analysis_client(self) -> TechnicalDrawingAnalysisClient:
        """Get technical drawing analysis client."""
        if self._drawing_analysis_client is None:
            # Create HTTP-based drawing analysis client
            # For now, we'll create a placeholder that would connect to the runtime service
            # In a real implementation, this would be an HTTP client
            self.logger.warning("Drawing analysis client not implemented for agent service provider")
            raise NotImplementedError("Drawing analysis client not available in agent service provider")
        return self._drawing_analysis_client
    
    def get_material_certificate_service(self) -> MaterialCertificateService:
        """Get material certificate service."""
        if self._material_certificate_service is None:
            # Create HTTP-based material certificate service client
            # For now, we'll create a placeholder that would connect to the runtime service
            # In a real implementation, this would be an HTTP client
            self.logger.warning("Material certificate service not implemented for agent service provider")
            raise NotImplementedError("Material certificate service not available in agent service provider")
        return self._material_certificate_service
    
    @classmethod
    def from_config(
        cls,
        runtime_service_url: str,
        lf: LogFactory = NO_LOG_FACTORY,
    ) -> "AgentServiceProvider":
        """Create service provider from configuration."""
        return cls(
            runtime_service_url=runtime_service_url,
            lf=lf,
        )
