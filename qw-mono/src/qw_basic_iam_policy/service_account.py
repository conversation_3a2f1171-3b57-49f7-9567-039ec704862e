"""Service account authentication models and policies."""
from datetime import datetime
from typing import Any, List

from pydantic import BaseModel

from qw_basic_iam_policy.interface import AuthP<PERSON>y, ValidAccessToken
from qw_log_interface import Logger


class ServiceAccountToken(BaseModel):
    """Service account token payload model."""

    service_name: str
    issuer: str = "qw-internal"
    subject: str  # service identifier (e.g., "service:mcp-server")
    scopes: List[str]  # permitted operations
    expiration: datetime

    def is_expired(self) -> bool:
        """Check if the token has expired."""
        return self.expiration < datetime.utcnow()


class ServiceAccountAuthContext:
    """Authentication context for service accounts."""

    def __init__(self, service_name: str, scopes: List[str], subject: str):
        self.service_name = service_name
        self.scopes = scopes
        self.subject = subject
        self.is_service_account = True

    def has_scope(self, scope: str) -> bool:
        """Check if the service account has a specific scope."""
        return scope in self.scopes

    def has_all_scopes(self, required_scopes: List[str]) -> bool:
        """Check if the service account has all required scopes."""
        return all(scope in self.scopes for scope in required_scopes)


class AllowServiceAccount(AuthPolicy):
    """Policy that allows only service accounts with required scopes."""

    def __init__(self, required_scopes: List[str]):
        self.required_scopes = required_scopes

    def eval(self, tkn: ValidAccessToken, logger: Logger) -> bool:
        """Evaluate policy for service account context."""
        if isinstance(tkn, ServiceAccountAuthContext):
            has_scopes = tkn.has_all_scopes(self.required_scopes)
            if not has_scopes:
                logger.warning(
                    f"Service account '{tkn.service_name}' missing required scopes: "
                    f"required={self.required_scopes}, available={tkn.scopes}"
                )
            return has_scopes

        # Not a service account context
        logger.warning("Policy requires service account but got user session")
        return False


class AllowUserOrService(AuthPolicy):
    """Policy that allows either user sessions or service accounts."""

    def __init__(self, user_policy: AuthPolicy, service_scopes: List[str]):
        self.user_policy = user_policy
        self.service_scopes = service_scopes

    def eval(self, tkn: ValidAccessToken, logger: Logger) -> bool:
        """Evaluate policy for either user or service account context."""
        if isinstance(tkn, ServiceAccountAuthContext):
            has_scopes = tkn.has_all_scopes(self.service_scopes)
            if not has_scopes:
                logger.warning(
                    f"Service account '{tkn.service_name}' missing required scopes: "
                    f"required={self.service_scopes}, available={tkn.scopes}"
                )
            return has_scopes
        else:
            # Assume it's a user token context - delegate to user policy
            return self.user_policy.eval(tkn, logger)
