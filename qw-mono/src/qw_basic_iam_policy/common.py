from typing import Union, TYPE_CHECKING
from qw_basic_iam_policy.interface import AuthPolicy
from qw_basic_iam_policy.token import ValidAccessToken
from qw_log_interface import Logger

if TYPE_CHECKING:
    from qw_basic_iam_policy.service_account import ServiceAccountAuthContext


class AllowAllSessions(AuthPolicy):
    def __init__(self, require_membership: bool = True):
        self.require_membership = require_membership

    def eval(self, tkn: Union[ValidAccessToken, "ServiceAccountAuthContext"], logger: Logger) -> bool:
        # Support both user tokens and service account contexts
        from qw_basic_iam_policy.service_account import ServiceAccountAuthContext

        if isinstance(tkn, ServiceAccountAuthContext):
            # For service accounts, always allow if they have basic API access
            return "api:read" in tkn.scopes or "api:write" in tkn.scopes
        else:
            # Traditional user token validation (ValidAccessToken)
            if self.require_membership:
                return tkn.membership.tenant_id is not None
            return True


class AllowIf(AuthPolicy):
    def __init__(self, condition: bool):
        self.condition = condition

    def eval(self, tkn: ValidAccessToken, logger: Logger) -> bool:
        return self.condition


class AllowForMembersFromTenant(AuthPolicy):
    def __init__(self, tenant_id: int):
        self.tenant_id = tenant_id

    def eval(self, tkn: ValidAccessToken, logger: Logger) -> bool:
        return tkn.membership.tenant_id == self.tenant_id
